const functions = require("firebase-functions");
const fetch = require("node-fetch");

exports.unsubscribe = functions.https.onRequest(async (req, res) => {
  const pathParts = req.path.split("/");
  console.log("Path Parts ---> ", pathParts);
  const base64Data = pathParts[pathParts.length - 1];
  if (!base64Data) {
    return res.status(400).send("No data provided");
  }

  try {
    const apiResponse = await fetch(`https://ifaxapp.com/live/ajax/broadcastUnsubscribe/${base64Data}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    const responseData = await apiResponse.json();
    if (responseData && responseData.url) {
      if (responseData.isUrlEmpty) {
        return res.status(200).json({
          message: "You have successfully unsubscribed"
        });
      } else {
        return res.redirect(302, responseData.url);
      }
    } else {
      if (responseData.isUrlEmpty) {
        return res.status(200).json({
          message: "You have successfully unsubscribed"
        });
      } else {
        return res.status(500).json({
          message: "Failed to process data"
        });
      }
    }
  } catch (error) {
    return res.status(500).json({
      message: "Failed to process data"
    });
  }
});
